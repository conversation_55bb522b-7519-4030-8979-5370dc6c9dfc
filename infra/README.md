<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| terraform | ~> 1.0 |
| aws | ~> 5.0 |
| datadog | ~> 3.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| dyn\_opt\_analytics\_service | git::https://github.com/ideasorg/terraform-aws-containerapp-base.git | v5 |
| dyn\_opt\_calibration\_req | terraform-aws-modules/sqs/aws | ~> 4.2.0 |
| dyn\_opt\_evaluation\_req | terraform-aws-modules/sqs/aws | ~> 4.2.0 |
| shared\_config | git::https://github.com/ideasorg/terraform-shared-config.git | v0 |

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy.sqs_access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_sns_topic_subscription.uen_dyn_opt_calibration_req_subscription](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sns_topic_subscription) | resource |
| [aws_sns_topic_subscription.uen_dyn_opt_evaluation_req_subscription](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sns_topic_subscription) | resource |
| [aws_sqs_queue_policy.allow_uen_to_send_events_calib_queue](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sqs_queue_policy) | resource |
| [aws_sqs_queue_policy.allow_uen_to_send_events_eval_queue](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sqs_queue_policy) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| autoscaling | Enable autoscaling based on CPU, Memory, or Requests per second | `any` | `{}` | no |
| aws\_region | AWS region | `string` | n/a | yes |
| capacity\_provider | Set of names of one or more capacity providers to associate with the cluster. Valid values also include FARGATE and FARGATE\_SPOT. | `string` | `"FARGATE"` | no |
| container\_max\_count | Mac copies of the container to run. | `number` | n/a | yes |
| container\_min\_count | Min copies of the container to run. | `number` | n/a | yes |
| dd\_index | DataDog tag to determine how long logs are indexed and available on DataDog for analysis. The default value is avm-15days. Possible options are avm-[7,15,30]days | `string` | n/a | yes |
| dyn\_opt\_s3\_bucket | s3 bucket name which will hold the calibration data | `string` | n/a | yes |
| dynamodb\_table | list of dynamodb table which hold data required by analytics | `list(string)` | n/a | yes |
| ecs\_cluster\_name | ECS cluster name | `string` | n/a | yes |
| ecs\_service\_name | ECS service name | `string` | n/a | yes |
| environment | The environment name in which the ECS Service is located. (e.g. dev, stage, prod) | `string` | n/a | yes |
| fds\_uen\_topic\_arn | FDS UEN SNS Arn | `string` | n/a | yes |
| log\_level | Root log level | `string` | `"INFO"` | no |
| nonspot\_minimum | The minimum number of containers to run on non-spot. | `number` | `1` | no |
| nonspot\_weight | Percentage of containers to run using nonspot; this plus spot\_weight must sum to 100. | `number` | `100` | no |
| per\_container\_cpu | The CPU units for the instances that Fargate will spin up. Options here: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html#fargate-tasks-size. | `number` | `1024` | no |
| per\_container\_memory | The memory units for the instances that Fargate will spin up. Options here: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html#fargate-tasks-size. | `number` | `2048` | no |
| repo\_url | URL to repo for DataDog linkage, e.g., https://github.com/ideasorg/ideas-springboot-microservice | `string` | n/a | yes |
| repository\_name | The name of the repository. Will be used as a tag for resources. | `string` | n/a | yes |
| secret\_manager\_name | Secrets manager name which holds fds uis auth token | `string` | `"dyn-opt-secret"` | no |
| service\_name | The name of the service. This is used to namespace all resources. | `string` | n/a | yes |
| service\_version | The version of the service being deployed | `string` | n/a | yes |
| spot\_minimum | The minimum number of containers to run on spot. | `number` | `0` | no |
| spot\_weight | Percentage of containers to run using spot; this plus nonspot\_weight must sum to 100. | `number` | `0` | no |
| team\_name | Team that owns the service | `string` | n/a | yes |
| teams\_channel\_email | Microsoft Teams Channel email for point of contact | `string` | n/a | yes |
| ucs\_base\_url | Base url for ucs service | `string` | n/a | yes |
| uis\_auth\_url | UIS service base url | `string` | n/a | yes |
| ups\_base\_url | Base url for ups service | `string` | n/a | yes |

## Outputs

No outputs.
<!-- END_TF_DOCS -->
