from enum import Enum


class EnvironmentVariable(Enum):
    FDS_UCS_TOKEN = 'FDS_UCS_TOKEN'
    AVAILABLE_CAPACITY_TABLE = 'AVAILABLE_CAPACITY_TABLE'
    DECISION_CHANGE_TABLE = 'DECISION_CHANGE_TABLE'
    G3_AUTH_TOKEN = 'G3_AUTH_TOKEN'
    IDP_WINDOW_TABLE = 'IDP_WINDOW_TABLE'
    IDP_COUNT_TABLE = 'IDP_COUNT_TABLE'
    UIS_AUTH_URL = 'UIS_AUTH_URL'
    SECRET_MANAGER_NAME = 'SECRET_MANAGER_NAME'
    UCS_BASE_URL = 'UCS_BASE_URL'
    UPS_BASE_URL = 'UPS_BASE_URL'
    FDS_UEN_ARN = "FDS_UEN_ARN"
    AWS_SNS_ENDPOINT_URL = 'AWS_SNS_ENDPOINT_URL'
    EVALUATION_REQ_URL = "EVALUATION_REQ_URL"
    CALIB_REQ_POLL_INTERVAL = 'CALIB_REQ_POLL_INTERVAL'
    LOCALSTACK_URL = 'LOCALSTACK_URL'
    CALIB_REQ_URL = 'CALIB_REQ_URL'
    LOG_LEVEL = 'LOG_LEVEL'
    S3_DATA_BUCKET_NAME = 'S3_DATA_BUCKET_NAME'
    CALIBRATED_POTENTIAL_TABLE = 'CALIBRATED_POTENTIAL_TABLE_NAME'
    ENV = 'ENV'
    AWS_REGION = 'AWS_REGION'
    ECS_CLUSTER_NAME = 'ECS_CLUSTER_NAME'
    ECS_SERVICE_NAME = 'ECS_SERVICE_NAME'
