from abc import ABC, abstractmethod
from typing import override

import pandas as pd

from src.common.http_service import HttpService


class G3AnyQueryRequest(ABC):

    _occupancyDate = 'occupancyDate'
    _leadTime = 'leadTime'
    _accomClassId = 'accomClassId'
    _accomCapacity = 'accomCapacity'
    _roomsSold = 'roomsSold'
    _availableCapacity = 'availableCapacity'
    _clientCode = 'clientCode'
    _propertyCode = 'propertyCode'
    _businessDate = 'businessDate'
    _decisionTypeName = 'decisionTypeName'
    _countBarDecisions = 'countBarDecisions'
    _businessDate13Weeks = 'businessDate13Weeks'
    _countBarDecisions13Weeks = 'countBarDecisions13Weeks'
    _localDate = 'localDate'
    _serverTime = 'serverTime'
    _avgAbsVariance = 'averageAbsoluteVariance'
    _dowName = 'dowName'
    _DTA = 'DTA'
    _decisionDate = 'decisionDate'
    _localDTTM = 'localDTTM'
    _name = 'name'
    _systemNewDecision = 'systemNewDecision'
    _systemOldDecision = 'systemOldDecision'
    _absDiff = 'absoluteDifference'
    _absPerc = 'absolutePercentage'
    _processingType = 'processingType'
    _preparedDate = 'preparedDate'
    _propertyId = 'propertyId'
    _timeZone = 'timeZone'

    @abstractmethod
    def get_query_params(self) -> dict[str, str]:
        pass

    @abstractmethod
    def get_query(self) -> str:
        pass

    @abstractmethod
    def get_columns(self) -> list[str]:
        pass


class PaceAccomActivityRequest(G3AnyQueryRequest):

    def __init__(self, start_date, end_date):
        self.start_date = start_date
        self.end_date = end_date

    @override
    def get_query_params(self) -> dict[str, str]:
        return {
            'startDate': self.start_date,
            'endDate': self.end_date
        }

    @override
    def get_query(self) -> str:
        return r'''select Occupancy_DT,         datediff(day, Business_Day_End_DT, Occupancy_DT) as leadTime,         at.accom_class_id,         sum(accom_capacity)                                             as accom_capacity,  sum(rooms_sold) as roomsSold  ,     sum(accom_capacity) - sum(rooms_not_avail_other) - sum(rooms_not_avail_maint) - sum(rooms_sold) as available_capacity  from pace_accom_activity pace           join accom_type at on at.accom_type_id = pace.accom_type_id and at.status_id = 1  where business_day_end_dt between '{startDate}' and '{endDate}'  group by Occupancy_DT, Business_Day_End_DT, at.accom_class_id  order by Occupancy_DT, Business_Day_End_DT, at.accom_class_id '''.format(
            **self.get_query_params())

    @override
    def get_columns(self) -> list[str]:
        return [self._occupancyDate, self._leadTime, self._accomClassId, self._accomCapacity, self._roomsSold, self._availableCapacity]

class DecisionVolumeComparisonRequest(G3AnyQueryRequest):

    @override
    def get_query_params(self) -> dict[str, str]:
        pass

    @override
    def get_query(self) -> str:
        return (r'''SELECT      t1.client_code,     t1.property_code,     t1.business_dt,     t1.decision_type_name,     t1.count_bar_decisions,     t2.business_dt_13_weeks,     t2.count_bar_decisions_13_weeks FROM      (         SELECT              p1.client_code,             p1.property_code,             d.business_dt,             dt.decision_type_name,             COUNT(*) AS count_bar_decisions         FROM              pace_dailybar_output pdbo             JOIN decision d ON d.decision_id = pdbo.decision_id             JOIN product p ON p.product_id = pdbo.product_id             JOIN property p1 ON p1.property_id = d.property_id             JOIN decision_type dt ON dt.decision_type_id = d.decision_type_id,             (                 SELECT MAX(d.business_dt) AS max_date                 FROM decision d                 JOIN pace_dailybar_output pdbo ON d.decision_id = pdbo.decision_id             ) mdb         WHERE              d.business_dt BETWEEN DATEADD(d, -7, mdb.max_date) AND mdb.max_date         GROUP BY              p1.client_code,              p1.property_code,              d.business_dt,              dt.decision_type_name     ) AS t1 LEFT JOIN      (         SELECT              p1.client_code,             p1.property_code,             d.business_dt AS business_dt_13_weeks,             dt.decision_type_name,             COUNT(*) AS count_bar_decisions_13_weeks         FROM              pace_dailybar_output pdbo             JOIN decision d ON d.decision_id = pdbo.decision_id             JOIN product p ON p.product_id = pdbo.product_id             JOIN property p1 ON p1.property_id = d.property_id             JOIN decision_type dt ON dt.decision_type_id = d.decision_type_id,             (                 SELECT MAX(d.business_dt) AS max_date                 FROM decision d                 JOIN pace_dailybar_output pdbo ON d.decision_id = pdbo.decision_id             ) mdb         WHERE              d.business_dt BETWEEN DATEADD(d, -7, DATEADD(week, -13, mdb.max_date)) AND DATEADD(week, -13, mdb.max_date)         GROUP BY              p1.client_code,              p1.property_code,              d.business_dt,              dt.decision_type_name     ) AS t2 ON      t1.client_code = t2.client_code     AND t1.property_code = t2.property_code     AND t1.decision_type_name = t2.decision_type_name     AND t1.business_dt = DATEADD(week, 13, t2.business_dt_13_weeks); ''')

    @override
    def get_columns(self) -> list[str]:
        return [self._clientCode, self._propertyCode, self._businessDate, self._decisionTypeName, self._countBarDecisions, self._businessDate13Weeks, self._countBarDecisions13Weeks]

class DecisionVolumeAnalysisRequest(G3AnyQueryRequest):

    @override
    def get_query_params(self) -> dict[str, str]:
        pass

    @override
    def get_query(self) -> str:
        return (r'''SELECT     client_code,     property_code,     business_dt,     local_dt,     server_time,     decision_type_name,     COUNT(*) AS count_bar_decisions,     AVG(absolute_diff) AS avg_abs_variance FROM     (         SELECT              p1.client_code,              p1.property_code,              d.business_DT,              CAST(FORMAT(d.caught_up_dttm, 'yyyy-MM-dd HH:mm:ss') AS varchar) AS local_dt,              CAST(FORMAT(d.createdate_dttm, 'yyyy-MM-dd HH:mm:ss') AS varchar) AS server_time,             dt.decision_type_name,             ABS(pdbo.double_rate - LAG(pdbo.double_rate) OVER (                 PARTITION BY                      p1.client_code,                      p1.property_code,                      pdbo.Occupancy_Date,                      pdbo.accom_type_id,                      pdbo.product_id                 ORDER BY                      pdbo.decision_id             )) AS absolute_diff         FROM              pace_dailybar_output pdbo             JOIN decision d ON pdbo.decision_id = d.decision_id             JOIN product p ON pdbo.product_id = p.product_id              JOIN property p1 ON d.property_id = p1.property_id             JOIN decision_type dt ON d.decision_type_id = dt.decision_type_id             JOIN (                 SELECT MAX(d.business_DT) AS max_date                 FROM decision d                  JOIN pace_dailybar_output pdbo ON d.Decision_ID = pdbo.Decision_ID             ) mbd ON d.business_DT BETWEEN DATEADD(DAY, -14, mbd.max_date) AND mbd.max_date         WHERE             pdbo.product_id = 1     ) AS absoluteDiff JOIN (     SELECT MAX(d.business_DT) AS max_date     FROM decision d      JOIN pace_dailybar_output pdbo ON d.Decision_ID = pdbo.Decision_ID ) mbd ON absoluteDiff.business_DT BETWEEN DATEADD(DAY, -7, mbd.max_date) AND mbd.max_date GROUP BY      client_code,     property_code,     business_dt,     local_dt,     server_time,     decision_type_name ORDER BY      business_dt, local_dt, server_time;''')

    @override
    def get_columns(self) -> list[str]:
        return [self._clientCode, self._propertyCode, self._businessDate, self._localDate, self._serverTime, self._decisionTypeName, self._countBarDecisions, self._avgAbsVariance]

class DecisionAnalysisRequest(G3AnyQueryRequest):

    @override
    def get_query_params(self) -> dict[str, str]:
        pass

    @override
    def get_query(self) -> str:
        return (r'''SELECT      client_code,     property_code,     occupancy_date,     dow_name,     DTA,     decision_date,     local_dttm,     decision_type_name,     name,     systemNewDecision,     systemOldDecision,     ABS(systemNewDecision - systemOldDecision) AS absolute,     CASE          WHEN systemOldDecision = 0 OR systemOldDecision IS NULL THEN NULL         ELSE (ABS(systemNewDecision - systemOldDecision) / systemOldDecision) * 100     END AS absolutePercentage FROM      (         SELECT              P1.client_code,             P1.property_code,             PDBO.Decision_ID,             PDBO.Accom_Type_ID,             PDBO.Product_ID,             PDBO.occupancy_date,             FORMAT(PDBO.occupancy_date, 'dddd') AS dow_name,              DATEDIFF(day, D.Business_DT, PDBO.Occupancy_Date) AS DTA,             D.business_dt AS decision_date,             CAST(FORMAT(D.caught_up_dttm, 'yyyy-MM-dd HH:mm:ss') AS varchar) AS local_dttm,             DT.decision_type_name,             P.name,             PDBO.double_rate AS systemNewDecision,             LAG(PDBO.double_rate) OVER (                 PARTITION BY                      P1.client_code,                      P1.property_code,                      PDBO.occupancy_date,                      PDBO.Accom_Type_ID,                      PDBO.Product_ID                  ORDER BY PDBO.Decision_ID             ) AS systemOldDecision          FROM             PACE_Dailybar_Output AS PDBO         JOIN decision AS D              ON D.Decision_ID = PDBO.Decision_ID         JOIN product AS P              ON P.Product_ID = PDBO.Product_ID         JOIN property AS P1              ON P1.Property_ID = D.Property_ID         JOIN Decision_Type AS DT              ON DT.Decision_Type_ID = D.Decision_Type_ID         JOIN (             SELECT MAX(d.business_DT) AS max_date             FROM decision d              JOIN pace_dailybar_output pdbo ON d.Decision_ID = pdbo.Decision_ID         ) MBD ON D.business_DT BETWEEN DATEADD(DAY, -14, MBD.max_date) AND MBD.max_date         WHERE             pdbo.Product_ID = 1     ) AS t JOIN (     SELECT MAX(d.business_DT) AS max_date     FROM decision d      JOIN pace_dailybar_output pdbo ON d.Decision_ID = pdbo.Decision_ID ) MBD ON t.decision_date BETWEEN DATEADD(DAY, -7, MBD.max_date) AND MBD.max_date ORDER BY      occupancy_date,      Accom_Type_ID,      Product_ID; ''')

    @override
    def get_columns(self) -> list[str]:
        return [self._clientCode, self._propertyCode, self._occupancyDate, self._dowName, self._DTA, self._decisionDate, self._localDate, self._decisionTypeName, self._name, self._systemNewDecision, self._systemOldDecision, self._absDiff, self._absPerc]

class InputProcessingRequest(G3AnyQueryRequest):

    def __init__(self, client_code, property_code):
        self.client_code = client_code
        self.property_code = property_code

    @override
    def get_query_params(self) -> dict[str, str]:
        return {
            'clientCode': self.client_code,
            'propertyCode': self.property_code
        }

    @override
    def get_query(self) -> str:
        return (r'''select pdp.client_code, pdp.property_code, ip.input_type, CAST(FORMAT(ip.prepared_dttm, 'yyyy-MM-dd HH:mm:ss') AS VARCHAR), pdp.property_time_zone from Input_Processing IP join Property_Daily_Processing PDP on IP.property_daily_processing_id = PDP.property_daily_processing_id where IP.Input_Type in ('CDP','BDE','CDP_ON_DEMAND')  and PDP.client_code like '{clientCode}'  and PDP.property_code like '{propertyCode}' and ip.prepared_dttm >= (select DATEADD(day,-8, MAX(Prepared_DTTM)) from input_processing ) and ip.status = 'COMPLETED' order by ip.prepared_dttm;''').format(
            **self.get_query_params())

    @override
    def get_columns(self) -> list[str]:
        return [self._clientCode, self._propertyCode, self._processingType, self._preparedDate, self._timeZone]

class PropertyIdRequest(G3AnyQueryRequest):

    def __init__(self, client_code, property_code):
        self.client_code = client_code
        self.property_code = property_code

    @override
    def get_query_params(self) -> dict[str, str]:
        return {
            'clientCode': self.client_code,
            'propertyCode': self.property_code
        }

    @override
    def get_query(self) -> str:
        return (r'''select c.client_code, p.property_code, p.property_id from property p  join client c  on p.client_id = c.client_id  where c.client_code like ('{clientCode}') and p.property_code like ('{propertyCode}');''').format(
            **self.get_query_params())

    @override
    def get_columns(self) -> list[str]:
        return [self._clientCode, self._propertyCode, self._propertyId]

class G3AnyApiService:
    ANY_API_PATH = 'api/G3Tables/executeQuery/v1'

    def __init__(self, http_service: HttpService, property_id):
        self.http_service = http_service
        self.property_id_param = {
            'propertyId': property_id
        }

    def fetch(self, request: G3AnyQueryRequest) -> pd.DataFrame:
        res = self.http_service.post(self.ANY_API_PATH, body=self.get_body(request.get_query()),
                                     params=self.property_id_param, headers={'Content-Type': 'application/json'})
        return pd.DataFrame(data=res, columns=request.get_columns())

    @staticmethod
    def get_body(query: str) -> dict[str, str]:
        return {'query': query}

class G3AnyApiGlobalService:
    ANY_API_PATH = 'api/G3Tables/executeQueryOnGlobal/v1'

    def __init__(self, http_service: HttpService, client_code):
        self.http_service = http_service
        self.client_code_param = {
            'clientCode': client_code
        }

    def fetch(self, request: G3AnyQueryRequest) -> pd.DataFrame:
        params = {**self.client_code_param}
        res = self.http_service.post(self.ANY_API_PATH, body=self.get_body(request.get_query()),
                                     params=params, headers={'Content-Type': 'application/json'})
        return pd.DataFrame(data=res, columns=request.get_columns())

    @staticmethod
    def get_body(query: str) -> dict[str, str]:
        return {'query': query}
