# --------------------------------------------------------------------------------------------------------------------------
# PARAMETERS
# These variables are expected to be passed in by the operator or via a corresponding tfvars file when calling this.
# --------------------------------------------------------------------------------------------------------------------------

variable "service_name" {
  description = "The name of the service. This is used to namespace all resources."
  type        = string

  validation {
    # 17 + 15 ("-internal-admin" the longest ending for aws_alb_target_group) = 32
    condition     = length(var.service_name) <= 17
    error_message = "The service_name cannot be longer than 17 characters because of ALB Target Group name restrictions."
  }
}

variable "repository_name" {
  description = "The name of the repository. Will be used as a tag for resources."
  type        = string
}

variable "environment" {
  description = "The environment name in which the ECS Service is located. (e.g. dev, stage, prod)"
  type        = string
}

variable "service_version" {
  description = "The version of the service being deployed"
  type        = string
}

variable "container_min_count" {
  description = "Min copies of the container to run."
  type        = number
}

variable "container_max_count" {
  description = "Mac copies of the container to run."
  type        = number
}

variable "per_container_cpu" {
  description = "The CPU units for the instances that Fargate will spin up. Options here: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html#fargate-tasks-size."
  type        = number
  default     = 1024
}

variable "per_container_memory" {
  description = "The memory units for the instances that Fargate will spin up. Options here: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html#fargate-tasks-size."
  type        = number
  default     = 2048
}

variable "capacity_provider" {
  description = "Set of names of one or more capacity providers to associate with the cluster. Valid values also include FARGATE and FARGATE_SPOT."
  type        = string
  default     = "FARGATE"
}

variable "spot_weight" {
  description = "Percentage of containers to run using spot; this plus nonspot_weight must sum to 100."
  type        = number
  default     = 0
}

variable "spot_minimum" {
  description = "The minimum number of containers to run on spot."
  type        = number
  default     = 0
}

variable "nonspot_weight" {
  description = "Percentage of containers to run using nonspot; this plus spot_weight must sum to 100."
  type        = number
  default     = 100
}

variable "nonspot_minimum" {
  description = "The minimum number of containers to run on non-spot."
  type        = number
  default     = 1
}

variable "dd_index" {
  description = "DataDog tag to determine how long logs are indexed and available on DataDog for analysis. The default value is avm-15days. Possible options are avm-[7,15,30]days"
  type        = string
}

###############################################################################
# DATADOG SERVICE CATALOG REGISTRATION
###############################################################################
variable "team_name" {
  description = "Team that owns the service"
  type        = string
}

variable "repo_url" {
  description = "URL to repo for DataDog linkage, e.g., https://github.com/ideasorg/ideas-springboot-microservice"
  type        = string
}

variable "teams_channel_email" {
  description = "Microsoft Teams Channel email for point of contact"
  type        = string
}

###############################################################################
# AUTOSCALING SETTINGS
###############################################################################
variable "autoscaling" {
  description = "Enable autoscaling based on CPU, Memory, or Requests per second"
  type        = any
  default     = {}
}


#######################
variable "dynamodb_table" {
  description = "list of dynamodb table which hold data required by analytics"
  type        = list(string)
}

variable "dyn_opt_s3_bucket" {
  description = "s3 bucket name which will hold the calibration data"
  type        = string
}

variable "log_level" {
  description = "Root log level"
  type        = string
  default     = "INFO"
}

variable "fds_uen_topic_arn" {
  description = "FDS UEN SNS Arn"
  type        = string
}

variable "secret_manager_name" {
  description = "Secrets manager name which holds fds uis auth token"
  default     = "dyn-opt-secret"
}

variable "ucs_base_url" {
  description = "Base url for ucs service"
  type        = string
}

variable "ups_base_url" {
  description = "Base url for ups service"
  type        = string
}

variable "uis_auth_url" {
  description = "UIS service base url"
  type        = string
}

variable "aws_region" {
  description = "AWS region"
  type        = string
}

variable "ecs_cluster_name" {
  description = "ECS cluster name"
  type        = string
}

variable "ecs_service_name" {
  description = "ECS service name"
  type        = string
}
