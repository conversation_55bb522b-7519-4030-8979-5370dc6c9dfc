import logging
from datetime import datetime
from pathlib import Path

import pandas as pd

from src.algorithm.evaluation_service import run_evaluation
from src.common import file_logger_service
from src.common.dto.evaluate_request import EvaluationRequestSource
from src.common.dto.evaluation_dynamodb_input import EvaluationDynamodbTableInput
from src.common.dto.evaluation_local_file_input import EvaluationLocalFileInput
from src.common.dto.evaluation_result import EvaluationResult
from src.common.dto.evaluation_s3_file_input import EvaluationS3FileInput
from src.common.dynamodb_service import DynamoDBService
from src.common.env import AVAILABLE_CAPACITY_TABLE
from src.common.s3_service import s3_service
from src.common.sns_service import SNSService
from src.common.ucs_service import get_calibration_rolling_window, should_use_leading_window, \
    should_fail_on_missing_occupancy_dates, get_pressure_floor, get_occ_change_threshold, \
    get_persist_eval_op_at_rc_lvl, get_max_idp_recommendation_cnt, get_total_scheduled_idp_count
from src.log_config.setup import setup_logger


class EvaluationService:

    def __init__(self, dynamodb_service=DynamoDBService(), sns_service=SNSService()):
        self.logger = logging.getLogger(__name__)
        self.dynamodb_service = dynamodb_service
        self.sns_service = sns_service

    def run_evaluate_using_s3_files(self, s3_path_holder: EvaluationS3FileInput) -> EvaluationResult:
        self.logger.info(f"Evaluating {s3_path_holder}")
        try:
            calib_values = {c.accom_class_id: c.calibrated_potential for c in s3_path_holder.calibrated_potential}
            return run_evaluation(s3_service.fetch_file, s3_path_holder.delta_occ_solds,
                                  s3_path_holder.reference_rate,
                                  s3_path_holder.delta_lrv, s3_path_holder.max_data_limit,
                                  s3_path_holder.min_data_requirement, calib_values)[0]
        except Exception as e:
            self.logger.exception(msg=f"Cannot evaluate with {s3_path_holder}")
            raise e

    def evaluate(self, evaluation_req_source: EvaluationRequestSource):
        self.logger.info(f"Evaluating {evaluation_req_source}")
        client = evaluation_req_source.get_client_code()
        property_c = evaluation_req_source.get_property_code()
        req_context = evaluation_req_source.request_context
        file_reader = lambda t: self.dynamodb_service.fetch_items_for_evaluation(t, client, property_c)
        idp_count_current_day = lambda ct: self.dynamodb_service.fetch_idp_count(client_code=client, property_code=property_c,
                                                                                 caught_up_dates=[ct])



        try:
            calib_values = {c.accom_class_id: c.calibrated_potential for c in
                            self.dynamodb_service.fetch_calibrated_potentials(
                                evaluation_req_source.request_context.calibrated_potential, client, property_c)}
            result, cap_date, debug_frame = run_evaluation(file_reader=file_reader,
                                                           delta_occ_solds=req_context.delta_occ_solds,
                                                           reference_rate=req_context.reference_rate,
                                                           available_capacity=AVAILABLE_CAPACITY_TABLE,
                                                           delta_lrv=req_context.delta_lrv,
                                                           window_size=get_calibration_rolling_window(client, property_c),
                                                           calibrated_value=calib_values,
                                                           use_leading_window=should_use_leading_window(client, property_c),
                                                           fail_on_missing_occ_dates=should_fail_on_missing_occupancy_dates(client, property_c),
                                                           pressure_floor=get_pressure_floor(client, property_c),
                                                           minHeuristicOccPercChangeThreshold=get_occ_change_threshold(client, property_c),
                                                           persist_eval_op_at_rc_lvl=get_persist_eval_op_at_rc_lvl(client, property_c),
                                                           max_idp_recom_cnt = get_max_idp_recommendation_cnt(client,property_c),
                                                           total_scheduled_idp_count = get_total_scheduled_idp_count(client, property_c),
                                                           idp_count_current_day=idp_count_current_day)
            evaluation_time = datetime.now().isoformat()
            file_logger_service.log_df(client, property_c, f'{cap_date}/assemblage_{evaluation_time}.csv', debug_frame)
            if result.should_optimize:
                self.logger.info(f"Sending SNS Notification with result {result}")
                self.sns_service.send_trigger_rms_optimization(client, property_c, result, cap_date, evaluation_time)
                return
            self.logger.info("Insignificant solds drift. Skipped triggering rms optimization")
        except Exception as e:
            self.logger.exception(msg=f"Cannot evaluate with {req_context}  due to {e}")
            raise e

    def run_evaluate_using_local_files(self, local_path_holder: EvaluationLocalFileInput) -> EvaluationResult:
        self.logger.info(f"Evaluating {local_path_holder}")
        try:
            if isinstance(local_path_holder.calibrated_potential, Path):
                calibrated_potential_df = pd.read_csv(local_path_holder.calibrated_potential,
                                                      index_col=['accom_class_id'])
                calib_values = {i: v[0] for i, v in calibrated_potential_df.iterrows()}
                return run_evaluation(pd.read_csv, local_path_holder.delta_occ_solds, local_path_holder.reference_rate,
                                      'no support',
                                      local_path_holder.delta_lrv, local_path_holder.max_data_limit,
                                      local_path_holder.min_data_requirement, calib_values)[0]
            calib_values = {c.accom_class_id: c.calibrated_potential for c in local_path_holder.calibrated_potential}
            return run_evaluation(pd.read_csv, local_path_holder.delta_occ_solds, local_path_holder.reference_rate,
                                  local_path_holder.delta_lrv, local_path_holder.max_data_limit,
                                  local_path_holder.min_data_requirement, calib_values)[0]

        except Exception as e:
            self.logger.exception(msg=f"Cannot evaluate with {local_path_holder}  due to {e}")
            raise e


if __name__ == '__main__':
    setup_logger()
    EvaluationService().evaluate(EvaluationRequestSource(client_code='Hilton', property_code='REKCU',
                                                         request_context=EvaluationDynamodbTableInput(
                                                             delta_occ_solds='dyn-opt-property-incremental-solds',
                                                             reference_rate='dyn-opt-ref-price-latest',
                                                             delta_lrv='dyn-opt-delta-lrv-latest',
                                                             min_data_requirement=21, max_data_limit=21,
                                                             calibrated_potential='dyn-opt-calibrated-potentials')))
