import logging

from fastapi import APIRouter
from fastapi import Request
import boto3
import requests

from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.sqs.listenersfactory import ListenersFactory

print(f" ---- {get_value(EnvironmentVariable.AWS_REGION, '')}")

log = logging.getLogger(__name__)
ecs_client = boto3.client('ecs', region_name=get_value(EnvironmentVariable.AWS_REGION, 'us-east-2'))

def get_ecs_instance_ips():
    """Fetch all running ECS instance IPs and log any errors."""
    try:
        task_arns = ecs_client.list_tasks(cluster=get_value(EnvironmentVariable.ECS_CLUSTER_NAME, ''), serviceName=get_value(EnvironmentVariable.ECS_SERVICE_NAME, '')).get("taskArns", [])
        if not task_arns:
            log.info("No running ECS tasks found.")
            return []

        tasks = ecs_client.describe_tasks(cluster=get_value(EnvironmentVariable.ECS_CLUSTER_NAME, ''), tasks=task_arns).get("tasks", [])
        if not tasks:
            log.info("No task details found.")

        instance_ips = {
            container["networkInterfaces"][0]["privateIpv4Address"]
            for task in tasks
            for container in task.get("containers", [])
            if "networkInterfaces" in container and container["networkInterfaces"]
        }

        return list(instance_ips)

    except boto3.exceptions.Boto3Error as e:
        log.error(f"AWS Boto3 error: {e}", exc_info=True)
    except Exception as e:
        log.error(f"Unexpected error while fetching ECS instance IPs: {e}", exc_info=True)

    return []

def execute_API(ip_address: str, port: str, prefix: str, api_endpoint: str):
    url = f"http://{ip_address}:{port}{prefix}{api_endpoint}"
    try:
        log.info(f'Executing {api_endpoint} ... ')
        if api_endpoint == '/health-check':
            response = requests.get(url, timeout=5)
        else:
            response = requests.post(url, timeout=5)
        log.info(f'Successfully executed {api_endpoint} for {ip_address}: {response.status_code} - {response.text}')
        return response.content
    except requests.RequestException as e:
        log.error(f'Failed to execute {api_endpoint} for {ip_address}: {e}')


router = APIRouter(prefix="/sqs-listeners", tags=['sqs-listeners'])

@router.post('/{status}/{process}')
def change_process_status(status: str, process: str):
    log.info('Changing process status...')
    ecs_instance_ips = get_ecs_instance_ips()
    result = {}
    for ip in ecs_instance_ips:
        content = execute_API(ip, '8000', router.prefix, f'/{status}-{process}')
        result[ip] = content
    log.info('Successfully changed process status.')
    return result


@router.post('/stop-calibration')
def stop_calibration(request: Request):
    calib_app = request.app.state.calib_app
    if calib_app.is_alive():
        log.info('Stopping Calibration...')
        calib_app.stop()
        calib_app.join()
        log.info('Successfully stopped calibration.')
    else:
        log.info('Calibration is not running.')

@router.post('/start-calibration')
def start_calibration(request: Request):
    calib_app = request.app.state.calib_app
    if calib_app.is_alive():
        log.info('Calibration is already running.')
    else:
        log.info('Starting Calibration...')
        listeners = ListenersFactory()
        request.app.state.calib_app = listeners.calibration_listener()
        request.app.state.calib_app.start()
        log.info('Successfully started calibration.')

@router.post('/stop-evaluation')
def stop_evaluation(request: Request):
    eval_app = request.app.state.eval_app
    if eval_app.is_alive():
        log.info('Stopping Evaluation...')
        eval_app.stop()
        eval_app.join()
        log.info('Successfully stopped evaluation.')
    else:
        log.info('Evaluation is not running.')

@router.post('/start-evaluation')
def start_evaluation(request: Request):
    eval_app = request.app.state.eval_app
    if eval_app.is_alive():
        log.info('Evaluation is already running.')
    else:
        log.info('Starting Evaluation...')
        listeners = ListenersFactory()
        request.app.state.eval_app = listeners.evaluation_listener()
        request.app.state.eval_app.start()
        log.info('Successfully started evaluation.')

@router.post('/stop-all')
def stop_all(request: Request):
    stop_calibration(request)
    stop_evaluation(request)

@router.post('/start-all')
def start_all(request: Request):
    start_calibration(request)
    start_evaluation(request)

@router.get('/health-check')
def health_check(request: Request):
    calib_app = request.app.state.calib_app
    eval_app = request.app.state.eval_app
    return {
        'calibration': 'ok' if calib_app and calib_app.is_alive() else 'not ok',
        'evaluation': 'ok' if eval_app and eval_app.is_alive() else 'not ok'
    }