import logging

from src.log_config.setup import setup_logger

setup_logger()
from src.sqs.listenersfactory import ListenersFactory
from src.sqs.sqs_listener_app import S<PERSON><PERSON><PERSON><PERSON>App

from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from src.controller.routers import calibrate, report, sqs_listener
from src.controller.routers import evaluation
from src.controller.routers import health_check
from src.controller.routers import log_management


@asynccontextmanager
async def lifespan(app: FastAPI):
    listeners = ListenersFactory()
    app.state.calib_app = listeners.calibration_listener()
    app.state.eval_app = listeners.evaluation_listener()

    app.state.calib_app.start()
    app.state.eval_app.start()

    yield

    app.state.calib_app.stop()
    app.state.calib_app.join()
    app.state.eval_app.stop()
    app.state.eval_app.join()


app = FastAPI(lifespan=lifespan)

app.include_router(calibrate.router)
app.include_router(evaluation.router)
app.include_router(health_check.router)
app.include_router(log_management.router)
app.include_router(report.router)
app.include_router(sqs_listener.router)

if __name__ == '__main__':
    setup_logger()
    uvicorn.run(app="__main__:app", host="localhost", port=8000, reload=True, workers=1)
