import asyncio
import logging
from datetime import date
from io import BytesIO

import pandas as pd
from awswrangler import s3
from pandas import Timestamp

from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value


class S3Service:
    logger = logging.getLogger(__name__)
    semaphore = asyncio.Semaphore(10)  # Limit concurrent reads

    async def read_csv_from_s3(self, s3_uri, usecols=None):
        return await asyncio.to_thread(
            pd.read_csv,
            s3_uri,
            usecols=usecols,
            storage_options={"anon": False}
        )

    async def async_download_folder_and_concat(self, s3_file_path, usecols=None):
        objects = self.list_objects(s3_file_path=s3_file_path, suffix='.csv')
        dfs = []

        for obj in objects:
            async with self.semaphore:
                try:
                    df = await self.read_csv_from_s3(obj, usecols=usecols)
                    file_name = obj.split('/')[-1]
                    eval_time_str = file_name.split('assemblage_')[1].split('.csv')[0]
                    df['evaluationTime'] = pd.to_datetime(eval_time_str, format='ISO8601')
                    dfs.append(df)
                except Exception as e:
                    self.logger.error(f"Error reading {obj}: {e}")

        return pd.concat(dfs, ignore_index=True) if dfs else pd.DataFrame()

    async def fetch_single_date_data(self, client_code: str, property_code: str, caught_up_date: str, usecols: None):
        base_path = f's3://{get_value(EnvironmentVariable.S3_DATA_BUCKET_NAME, 'do_table')}/{client_code}/{property_code}/{caught_up_date}/'
        try:
            df = await self.async_download_folder_and_concat(s3_file_path=base_path, usecols=usecols)
            df['caughtUpDate'] = pd.to_datetime(caught_up_date, format='ISO8601')
            return df
        except Exception as e:
            self.logger.error(f'Failed to fetch assmeblage for {client_code}, {property_code}, {caught_up_date}')
            return None


    async def fetch_files_parallel(self, client_code: str, property_code: str, caught_up_dates: list[date], usecols: None):
        caught_up_dates = [d.strftime('%Y-%m-%d') for d in caught_up_dates]
        tasks = [
            self.fetch_single_date_data(client_code, property_code, d, usecols)
            for d in caught_up_dates
        ]
        results = await asyncio.gather(*tasks)
        return pd.concat([df for df in results if df is not None], ignore_index=True) if results else pd.DataFrame()

    def fetch_assemblage_files(self, client_code: str, property_code: str, caught_up_date: date, usecols=None):
        caught_up_dates = pd.date_range(end=caught_up_date, periods=7).tolist()
        try:
            self.logger.info(f"Fetching assemblage files for client_code: {client_code}, property_code: {property_code}")
            return asyncio.run(self.fetch_files_parallel(client_code=client_code, property_code=property_code, caught_up_dates=caught_up_dates, usecols=usecols))
        except Exception as e:
            self.logger.error(f'{property_code} failed: {e}')

    @staticmethod
    def fetch_file(s3_file_path, parse_dates=[]) -> pd.DataFrame:
        return s3.read_csv([s3_file_path], parse_dates=parse_dates)

    @staticmethod
    def save_df(s3_file_path, df):
        s3.to_csv(df, s3_file_path, index=False)

    def save_file(self, binary, path):
        s3.upload(binary, path)

    @staticmethod
    def get_parent(s3_file_path):
        if s3_file_path[-1] == r'/':
            s3_file_path = s3_file_path[:-1]
        rev: str = s3_file_path.replace(r's3://', '')[::-1]
        splited = rev.split(r'/', 1)
        parent = '' if len(splited) < 2 else splited[1]
        return fr's3://{parent[::-1]}'

    @staticmethod
    def get_file_name(s3_file_path):
        if s3_file_path[-1] == r'/':
            s3_file_path = s3_file_path[:-1]
        rev: str = s3_file_path.replace(r's3://', '')[::-1]
        splited = rev.split(r'/', 1)
        parent = '' if len(splited) < 2 else splited[0]
        return fr'{parent[::-1]}'

    def download_folder_and_concat(self, s3_file_path):
        objects = s3.list_objects(s3_file_path, suffix='.csv')
        dfs = []
        for obj in objects:
            df = s3.read_csv(path=obj)
            df['evaluationTime'] = df['evaluationTime'] = self.get_file_name(obj).split('assemblage_')[1].split(".csv")[0]
            df['evaluationTime'] = pd.to_datetime(df['evaluationTime'], infer_datetime_format=True)
            dfs.append(df)
        return pd.concat(dfs, ignore_index=True) if len(dfs) > 0 else pd.DataFrame()

    def list_objects(self, s3_file_path, suffix=''):
        objects = s3.list_objects(s3_file_path, suffix=suffix)
        return objects

    def upload_file(self, file_like, path):
        s3.upload(file_like, path)

    def donwload_file_like(self, s3_path: str):
        file_like = BytesIO()
        s3.download(s3_path, local_file=file_like)
        file_like.seek(0)
        return file_like

s3_service = S3Service()


def save_df_in_parent(s3_path, file_name, df):
    file_path = f'{s3_service.get_parent(s3_path)}/{file_name}'
    s3_service.save_df(file_path, df)

if __name__ == '__main__':
    from datetime import date
    print(f'Start time - {Timestamp.now()}')
    property_code = 'ABUHI'
    caught_up_date = date(2025,5,29)
    df = s3_service.fetch_assemblage_files(
        client_code='Hilton',
        property_code=property_code,
        caught_up_date=caught_up_date,
        # usecols=['accomClassId','occupancyDate','leadTime','deltaLRV','lrv','ceilingValue','price','deltaSolds','captureDate','availableCapacity','pressure']
    )

    df.to_csv(f"C:/Users/<USER>/Downloads/assemblage_{property_code}_{caught_up_date.isoformat()}.csv", index=False)
    print(f'End time - {Timestamp.now()}')

