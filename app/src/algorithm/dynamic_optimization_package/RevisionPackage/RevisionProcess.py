import logging
import pathlib
from datetime import timedelta

import pandas as pd
import numpy as np
from functools import partial
import functools
from dataclasses import dataclass
import pickle
import time

from src.algorithm.dynamic_optimization_package.RevisionPackage.AdjustmentReason import AdjustmentReason, ControlsConditions
from src.algorithm.dynamic_optimization_package.RevisionPackage.ColumnsContainer import RevisionColumnsContainer
from src.algorithm.dynamic_optimization_package.RevisionPackage.EvalChangeDO import Eval<PERSON>hangeD<PERSON>
from src.algorithm.dynamic_optimization_package.RevisionPackage.ProcessDescriptionContainer import ProcessDescription
from src.algorithm.dynamic_optimization_package.RevisionPackage.RewriteHistorySimulation import RewriteHistorySimulation
from src.algorithm.dynamic_optimization_package.CommonDynamicOptElements import CommonDOElements
from src.algorithm.dynamic_optimization_package.RevisionPackage.fast_deep_copy import fast_deepcopy
from src.algorithm.dynamic_optimization_package.RevisionPackage.AddIncrementalsToAssemblage import AddIncrementalsToAssemblage
from src.common.utilities.DateUtil import DateUtil


@dataclass
class RevisionProcess:
    pressure_col: str
    potential_col: str
    day_col: str
    eval_time_col: str
    accom_class_col: str
    lrv_col: str
    price_col: str
    delta_lrv_col: str
    caught_up_date_col: str
    delta_solds_col: str
    inc_solds_col: str
    ceil_col: str
    ref_rate_col: str
    lrv_col: str
    lead_time_col: str
    avail_cap_col: str
    calib_potential_col: str = 'calibPotential'
    _is_dyn_opt = 'isDynOpt'
    _is_opt = 'isOpt'
    _prev_event = 'prevEvent'
    _property_code = 'propertyCode'
    _percentile = 'ptile'
    _opt_count = 'optCount'
    _follows_reset_event = 'followsResetEvent'
    _shift_price = 'shiftPrice'
    _shift_delta_lrv = 'shiftDeltaLRV'
    _shift_lrv = 'shiftLRV'
    _abs_perc_shift_price = 'absPercShiftPrice'
    _abs_perc_shift_delta_lrv = 'absPercShiftDeltaLRV'
    _abs_perc_shift_lrv = 'absPercShiftLRV'
    _has_run = 'hasRun'
    _has_change = 'hasChange'
    _has_action = 'hasAction'
    _eval_day = 'evalDay'
    _action_order_count = 'actionOrderCount'
    logger = logging.getLogger(__name__)

    def __post_init__(self):
        self.rc = RevisionColumnsContainer(
            dayCol=self.day_col, evalTimeCol=self.eval_time_col, accomClassCol=self.accom_class_col, lrvCol=self.lrv_col
            , priceCol=self.price_col, deltaLRVCol=self.delta_lrv_col, deltaSoldsCol=self.delta_solds_col,
            incSoldsCol=self.inc_solds_col
            , ceilCol=self.ceil_col, refRateCol=self.ref_rate_col, availCapCol=self.avail_cap_col
        )

    def run_revision(self
                     , assem: pd.DataFrame
                     , waste_threshold: float
                     , regret_threshold: float
                     , qfun: dict
                     , current_calib_percentile: float
                     , current_target_avg_num_opts:float
                     , max_num_opts_per_day:float
                     , min_date_for_eval:pd.Timestamp
                     , processing_data:pd.DataFrame
                     , process_time_col:str= "preparedDate"
                     , opt_type_col:str= "processingType"
                     , min_shift_delta_lrv_thresh: float = 0.015
                     , max_shift_delta_lrv_thresh: float = 0.10
                     , min_shift_lrv_thresh: float = 0.025
                     , max_shift_lrv_thresh: float = 0.10
                     , override_target: float | None = None
                     , sim_type: str = 'simplified'
                     , eval_args: dict | None = None
                     , percentiles_loc: pathlib.Path | None = None
                     , opt_details_loc: pathlib.Path | None = None
                     , pcode: str = ""
                     , enforce_directionality: bool = True
                     , lower_percentile_bound: float = 5.
                     , upper_percentile_bound: float = 97.
                     , tolerable_binary_search_error:float=0.1
                     , override_to_full_window_update: bool = False
                     , include_scheduled_processings:bool=False
                     , prefer_observed:bool=False
                     , min_num_seconds_between_opts: int | float | None=None
                     , max_num_dyn_opts_per_day: int | float | None=None
                     ):
        np.seterr(divide = 'ignore')
        # calculate the potential and do some currentState clean up
        assem, scheduled_process_times = self.prep_data_frame_for_revision(assem=assem, qfun=qfun, processing_data=processing_data, process_time_col=process_time_col, opt_type_col=opt_type_col, min_date_for_eval=min_date_for_eval)
        target, adjustment_type, observed_nopts, med_max_shift_lrv, med_max_shift_delta_lrv = \
                            self.determine_target_number_of_opts(assem=assem, max_num_opts_per_day=max_num_opts_per_day,
                                                                 current_calib_percentile=current_calib_percentile
                                                                 , qfun=qfun, waste_threshold=waste_threshold
                                                                 , eval_args=eval_args
                                                                 , sim_type=sim_type
                                                                 , regret_threshold=regret_threshold
                                                                 , min_shift_delta_lrv_thresh=min_shift_delta_lrv_thresh
                                                                 , max_shift_delta_lrv_thresh=max_shift_delta_lrv_thresh
                                                                 , min_shift_lrv_thresh=min_shift_lrv_thresh
                                                                 , max_shift_lrv_thresh=max_shift_lrv_thresh
                                                                 , override_target=override_target
                                                                 , current_target_avg_num_opts=current_target_avg_num_opts
                                                                 , override_to_full_window_update=override_to_full_window_update
                                                                 , include_scheduled_processings=include_scheduled_processings
                                                                 , prefer_observed=prefer_observed
                                                                 , min_num_seconds_between_opts= min_num_seconds_between_opts
                                                                 , max_num_dyn_opts_per_day= max_num_dyn_opts_per_day
                                                                 , scheduled_process_times= scheduled_process_times)


        if adjustment_type == AdjustmentReason.potential_in_spec \
                or abs(target - observed_nopts) <= tolerable_binary_search_error:  # there will be nothing to compute further
            return current_calib_percentile, target, observed_nopts, adjustment_type, med_max_shift_lrv, med_max_shift_delta_lrv

        test_percentile, opt_details_df = self.revise_in_bounds(assem=assem, sim_type=sim_type, target=target,
                                                                current_calib_percentile=current_calib_percentile,
                                                                percentiles_loc=percentiles_loc, eval_args=eval_args, pcode=pcode, qfun=qfun
                                                                , lower_percentile_bound=lower_percentile_bound, upper_percentile_bound=upper_percentile_bound
                                                                , tolerable_binary_search_error=tolerable_binary_search_error
                                                                , override_to_full_window_update=override_to_full_window_update, min_num_seconds_between_opts=min_num_seconds_between_opts)

        # TODO: Do I need a check for testPercentile==np.nan?
        if not opt_details_df is None:
            if opt_details_loc:
                opt_details_df[self._property_code] = pcode
                opt_details_df.to_parquet(opt_details_loc, index=False)

        # removed direction checks and bounds checks becauise it should be impossible for that to happen generally.
        # Atleast when preferObserved == True. If the code can be brought entirely inline then this will
        # no longer be the case.
        return test_percentile, target, observed_nopts, adjustment_type,med_max_shift_lrv, med_max_shift_delta_lrv


    def revise_in_bounds(self, assem: pd.DataFrame, sim_type: str, target: float, current_calib_percentile: float,
                         qfun: dict, percentiles_loc: pathlib.Path, eval_args: dict, pcode: str,
                         upper_percentile_bound:float=97, lower_percentile_bound:float=5, tolerable_binary_search_error:float=0.1,
                         override_to_full_window_update:bool=False, min_num_seconds_between_opts: int | float | None=None,
                         scheduled_process_times: list[pd.Timestamp] | None=None):
        try:
            if sim_type != 'complex':
                test_percentiles = self.generate_simplified(assem=assem.copy(), qfun=qfun)
                test_percentile = -1
                if percentiles_loc:
                    test_percentiles.to_csv(percentiles_loc)
                test_percentile = self.find_target_percentile(calib_res=test_percentiles, target_avg_op=target,
                                                              avg_calib_per_day_col='optCount', ptile_col='ptile')
                opt_details_df = None
            else:
                # this dataset will be used again and again and again so I don't really want to have to
                # recreate it.
                ECDO = EvalChangeDO.from_dataframe(assemblage=assem, rc=self.rc, scheduled_process_times=scheduled_process_times)
                test_percentile, opt_details_df = self.binary_percentile_search(ECDO=ECDO, assem=assem,
                                                                                current_percentile=current_calib_percentile,
                                                                                target_average_opts=target, pcode=pcode, qfun=qfun,
                                                                                eval_args=eval_args, min_percentile_boundary=lower_percentile_bound,
                                                                                max_percentile_boundary=upper_percentile_bound, percentiles_loc=percentiles_loc
                                                                                , tolerable_binary_search_error=tolerable_binary_search_error
                                                                                , override_to_full_window_update=override_to_full_window_update, min_num_seconds_between_opts=min_num_seconds_between_opts)
            return test_percentile, opt_details_df
        except Exception as e:
            self.logger.error(f'Failed to Revise in Bounds: {e}')
            return current_calib_percentile, None

    def get_nopts_complex_default_case(self, assem:pd.DataFrame, current_calib_percentile, qfun: dict, eval_args: dict
                                       , override_to_full_window_update:bool=False, include_scheduled_processings:bool=False
                                       , min_num_seconds_between_opts: int | float | None=None
                                       , max_num_dyn_opts_per_day: int | float | None=None, scheduled_process_times: list | None=None):

        ECDO = EvalChangeDO.from_dataframe(assemblage=assem, rc=self.rc, scheduled_process_times=scheduled_process_times)
        n_days = assem[self.caught_up_date_col].nunique()
        current_n_opts_per_day, opt_details_df = self.evaluate_percentile_complex(ECDO=ECDO, percentile=current_calib_percentile,
                                                                                  qfun=qfun, n_days=n_days, eval_args=eval_args
                                                                                  , override_to_full_window_update=override_to_full_window_update
                                                                                  , include_scheduled_processings=include_scheduled_processings
                                                                                  , min_num_seconds_between_opts=min_num_seconds_between_opts
                                                                                  , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day
                                                                                  )
        return current_n_opts_per_day

    def test_percentile(self, guess_value, target_value, delta_thresh):
        if abs(guess_value - target_value) <= delta_thresh:
            return AdjustmentReason.potential_in_spec
        elif guess_value < target_value:
            return AdjustmentReason.potential_too_low_increase_percentile
        else:
            return AdjustmentReason.potential_too_high_decrease_percentile

    def binary_percentile_search(self, ECDO: EvalChangeDO, assem: pd.DataFrame, current_percentile
                                 , target_average_opts, pcode: str, qfun: dict, eval_args: dict
                                 , min_percentile_boundary: float = 0, max_percentile_boundary: float = 100
                                 , percentiles_loc: pathlib.Path | None = None, tolerable_binary_search_error:float=0.1
                                 , override_to_full_window_update:bool=False, include_scheduled_processings:bool=False
                                 , min_num_seconds_between_opts: int | float | None=None, max_num_dyn_opts_per_day: int | float | None=None):
        n_days = assem[self.caught_up_date_col].nunique()
        percentile_list = []
        opt_details_list = []
        current_nopts_per_day, opt_details_df = self.evaluate_percentile_complex(ECDO=ECDO, percentile=current_percentile
                                                                                , qfun=qfun, n_days=n_days, eval_args=eval_args
                                                                                , override_to_full_window_update=override_to_full_window_update
                                                                                , include_scheduled_processings=include_scheduled_processings
                                                                                , min_num_seconds_between_opts=min_num_seconds_between_opts
                                                                                , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day)
        opt_details_list.append(opt_details_df)
        current_state = self.test_percentile(guess_value=current_nopts_per_day, target_value=target_average_opts,
                                             delta_thresh=0.1)
        # these will look backwards because if the nopts is too low we need to decrease the current percentile
        if current_state == AdjustmentReason.potential_in_spec:
            return current_percentile
        elif current_state == AdjustmentReason.potential_too_high_decrease_percentile:
            test_percentile_high = max_percentile_boundary
            test_percentile_low = current_percentile
        else:
            test_percentile_high = current_percentile
            test_percentile_low = min_percentile_boundary

        update_threshold = 0.01
        if percentiles_loc:
            percentile_list = [[current_percentile, current_nopts_per_day]]
        best_guess = current_percentile
        best_guess_margin_of_error = abs(current_nopts_per_day - target_average_opts)
        current_percentile_guess = current_percentile
        iteration_counter = 1
        while not current_state == AdjustmentReason.potential_in_spec and iteration_counter < 14:
            prev_guess = current_percentile_guess
            current_percentile_guess = (test_percentile_high + test_percentile_low) / 2
            update_shift = abs(prev_guess - current_percentile_guess)
            if update_shift < update_threshold :#or abs(currentPercentileGuess-minPercentileBoundary)<updateThreshold or abs(currentPercentileGuess-maxPercentileBoundary)<updateThreshold:
                # early stopping condition
                break
            test_nopts_per_day, opt_details_df = self.evaluate_percentile_complex(ECDO=ECDO, percentile=current_percentile_guess,
                                                                             qfun=qfun, n_days=n_days, eval_args=eval_args
                                                                             , override_to_full_window_update=override_to_full_window_update
                                                                             , include_scheduled_processings=include_scheduled_processings
                                                                             , min_num_seconds_between_opts=min_num_seconds_between_opts
                                                                             , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day)
            opt_details_list.append(opt_details_df)
            current_state = self.test_percentile(guess_value=test_nopts_per_day, target_value=target_average_opts, delta_thresh=0.1)
            margin_of_error = abs(test_nopts_per_day - target_average_opts)
            if margin_of_error < best_guess_margin_of_error:
                best_guess_margin_of_error=margin_of_error
                best_guess = current_percentile_guess
            if current_state == AdjustmentReason.potential_too_high_decrease_percentile:
                test_percentile_low = current_percentile_guess
            else:
                test_percentile_high = current_percentile_guess

            iteration_counter += 1
            if percentiles_loc:
                percentile_list.append([current_percentile_guess, test_nopts_per_day])
                odf = pd.DataFrame(percentile_list, columns=[self._percentile, self._opt_count])
                odf[self._property_code] = pcode
                odf.to_csv(percentiles_loc)
            if best_guess_margin_of_error<tolerable_binary_search_error:
                current_state = AdjustmentReason.potential_in_spec
        opt_details_df = pd.concat(opt_details_list,ignore_index=True)

        return best_guess, opt_details_df

    def simplified_get_current_nopts_per_day(self, assem: pd.DataFrame, current_calib_percentile: float, qfun: dict, include_scheduled_processings:bool=False) -> float:
        n_days = assem[self.caught_up_date_col].nunique()  # TODO: should I instead take these as input?

        assem = self.apply_percentile(assem=assem, ptile=current_calib_percentile, qfun=qfun)
        # caluclate the number of opts. Question: Do we want no days with waste/regret or average no waste/regret
        n_dyn_opts = self.calculate_num_opts_simple_version(assem=assem.copy(), include_scheduled_processings=include_scheduled_processings)

        # calculate the average opts per day
        return n_dyn_opts / n_days

    def get_current_n_opts(self, sim_type:str, assem:pd.DataFrame, current_calib_percentile:float,
                           qfun:dict, include_scheduled_processings:bool, override_to_full_window_update:bool=False,
                           eval_args: dict | None=None, min_num_seconds_between_opts: int | float | None=None,
                           max_num_dyn_opts_per_day: int | float | None=None, scheduled_process_times: list | None=None):

        if not sim_type == "complex":
            current_opts_per_day = self.simplified_get_current_nopts_per_day(assem=assem,
                                                                             current_calib_percentile=current_calib_percentile,
                                                                             qfun=qfun,
                                                                             include_scheduled_processings=include_scheduled_processings)
        else:
            if eval_args is None:
                eval_args = dict()
            current_opts_per_day = self.get_nopts_complex_default_case(assem=assem, current_calib_percentile=current_calib_percentile,
                                                                       qfun=qfun, eval_args=eval_args,
                                                                       include_scheduled_processings=include_scheduled_processings,
                                                                       override_to_full_window_update=override_to_full_window_update
                                                                       , min_num_seconds_between_opts=min_num_seconds_between_opts
                                                                       , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day, scheduled_process_times=scheduled_process_times)
        return current_opts_per_day

    def determine_target_number_of_opts(self, assem: pd.DataFrame, current_calib_percentile: float, qfun: dict,
                                        waste_threshold: float, regret_threshold: float, current_target_avg_num_opts:float, max_num_opts_per_day:float,
                                        sim_type:str, eval_args: dict | None=None,
                                        min_shift_delta_lrv_thresh: float = 0.015,
                                        max_shift_delta_lrv_thresh: float = 0.10,
                                        min_shift_lrv_thresh: float = 0.025, max_shift_lrv_thresh: float = 0.10,
                                        override_target: float | None = None, override_to_full_window_update:bool=False, include_scheduled_processings:bool=False, prefer_observed:bool=False
                                        , min_num_seconds_between_opts: int | float | None=None, max_num_dyn_opts_per_day: int | float | None=None, scheduled_process_times: list | None=None):

        current_opts_per_day = self.get_current_n_opts(sim_type=sim_type, assem=assem, current_calib_percentile=current_calib_percentile
                                                       , qfun=qfun, eval_args=eval_args, include_scheduled_processings=include_scheduled_processings
                                                       , override_to_full_window_update=override_to_full_window_update, min_num_seconds_between_opts=min_num_seconds_between_opts
                                                       , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day, scheduled_process_times=scheduled_process_times)
        if not override_target is None:
            target=override_target
            adjustment_type=AdjustmentReason.is_override_target_num_opts
            med_max_shift_lrv, med_max_shift_delta_lrv = np.nan, np.nan
        else:
            # come back and add a simulate complex to see if they match
            target, adjustment_type = self.find_target_if_waste_or_regret(current_nopts_per_day=current_opts_per_day,
                                                                          waste_threshold=waste_threshold
                                                                          , regret_threshold=regret_threshold)
            if adjustment_type == AdjustmentReason.potential_in_spec:
                target = None
                # generating this so that I know which steps have an optimization
                # pot = self.generateCalibPotentialDataFrame(qfun=qfun, currentCalibPercentile=currentCalibPercentile)
                target, adjustment_type,med_max_shift_lrv, med_max_shift_delta_lrv = self.find_target_when_inbounds(assem=assem, current_calib_percentile=current_calib_percentile
                                                                                                                    , qfun=qfun, current_nopts_per_day=current_opts_per_day
                                                                                                                    , waste_threshold=waste_threshold
                                                                                                                    , regret_threshold=regret_threshold
                                                                                                                    , current_target_avg_num_opts=current_target_avg_num_opts
                                                                                                                    , max_num_opts_per_day=max_num_opts_per_day
                                                                                                                    , min_shift_delta_lrv_thresh=min_shift_delta_lrv_thresh
                                                                                                                    , max_shift_delta_lrv_thresh=max_shift_delta_lrv_thresh
                                                                                                                    , min_shift_lrv_thresh=min_shift_lrv_thresh
                                                                                                                    , max_shift_lrv_thresh=max_shift_lrv_thresh
                                                                                                                    , prefer_observed= prefer_observed)
            else:
                med_max_shift_lrv, med_max_shift_delta_lrv = np.nan, np.nan
        return target, adjustment_type, current_opts_per_day, med_max_shift_lrv, med_max_shift_delta_lrv

    def preprocess_input_processing_data(self, processing_data: pd.DataFrame, process_time_col: str):
        processing_data[process_time_col] = pd.to_datetime(processing_data[process_time_col], format='ISO8601')
        processing_data[process_time_col] = processing_data[process_time_col] + timedelta(days=1)
        processing_data[process_time_col] = processing_data.apply(lambda x: DateUtil.convert_to_gmt(x[process_time_col].strftime('%Y-%m-%d %H:%M:%S'), x['timeZone']), axis = 1)
        return processing_data

    def prep_data_frame_for_revision(self, assem: pd.DataFrame, qfun:dict, processing_data:pd.DataFrame, min_date_for_eval:pd.Timestamp, process_time_col:str= "processingTime", opt_type_col:str= "input_type"):

        processed_data = self.preprocess_input_processing_data(processing_data=processing_data, process_time_col=process_time_col)

        # calculate the potentials for the whole file.
        assem[self.potential_col] = np.log(assem[self.pressure_col])
        assem[self.potential_col] = assem[self.potential_col].replace([np.inf, -np.inf], np.nan)

        incrementalizer = AddIncrementalsToAssemblage(rc=self.rc)
        assem, scheduled_process_times = incrementalizer.add_incrementals_to_assemblage(assem=assem, input_process=processed_data, process_time_col=process_time_col, opt_type_col=opt_type_col)
        assem[self.inc_solds_col]=assem[self.inc_solds_col].fillna(0.)
        assem = assem.query(f'{self.rc.evalTimeCol}>=@min_date_for_eval')
        # if it's impossible for a particular accom class to trigger an optimization we want to delete it from the dataset
        # and not waste compute cycles on it. This will be triggered by one not having a usable qfun attached to it
        for last_modified, qfun_dict in qfun.items():
            accom_del_list = [ac for ac, fun in qfun_dict.items() if not isinstance(fun, functools.partial)]
            for ac in accom_del_list:
                del qfun_dict[ac]
            assem = assem.query(f'not {self.accom_class_col} in @accom_del_list').reset_index(drop=True)
        return assem, scheduled_process_times

    def find_target_when_inbounds(self, assem: pd.DataFrame, current_calib_percentile:float, qfun:dict,
                                  current_nopts_per_day: float, waste_threshold: float, regret_threshold: float,
                                  min_shift_delta_lrv_thresh: float, max_shift_delta_lrv_thresh: float,
                                  min_shift_lrv_thresh: float, max_shift_lrv_thresh: float, max_num_opts_per_day: float
                                  , current_target_avg_num_opts:float, prefer_observed:bool=False):
        '''
        Adjusts up or down based on what the changes in LRV and deltaLRV were
        :param current_target_avg_num_opts:
        :param assem:
        :param pot:
        :param current_nopts_per_day:
        :param waste_threshold:
        :param regret_threshold:
        :param min_shift_delta_lrv_thresh:
        :param max_shift_delta_lrv_thresh:
        :param min_shift_lrv_thresh:
        :param max_shift_lrv_thresh:
        :return:
        '''
        # TODO: We could combine this with the simulation at some point to more directly find the optimal percentile.

        #  sort order the dataset
        assem = assem.sort_values([self.accom_class_col, self.day_col, self.eval_time_col], ignore_index=True)

        # subset to only the necessary components
        ass2 = assem[[self.accom_class_col, self.day_col, self.eval_time_col, self.lrv_col, self.price_col, self.delta_lrv_col, self._follows_reset_event]].copy()

        # calculate the change in rate and deltaLRV since the last evaluation
        # the shift here takes the next evaluation. Verified this is the correct logic with the current sorting scheme
        ass2[self._shift_price] = ass2[self.price_col] - ass2.groupby([self.accom_class_col, self.day_col])[self.price_col].shift(1)
        ass2[self._shift_delta_lrv] = ass2[self.delta_lrv_col] - ass2.groupby([self.accom_class_col, self.day_col])[self.delta_lrv_col].shift(1)
        ass2[self._shift_lrv] = ass2[self.lrv_col] - ass2.groupby([self.accom_class_col, self.day_col])[self.lrv_col].shift(1)
        ass2[self._abs_perc_shift_price] = np.abs(ass2[self._shift_price] / ass2[self.price_col])
        ass2[self._abs_perc_shift_delta_lrv] = np.abs(ass2[self._shift_delta_lrv] / ass2[self.price_col])
        ass2[self._abs_perc_shift_lrv] = np.abs(ass2[self._shift_lrv] / ass2[self.price_col])

        # create a list of columns to append to the assemblage file and the join keys
        cols = [self.accom_class_col, self.day_col, self.eval_time_col, self._abs_perc_shift_price, self._abs_perc_shift_delta_lrv, self._abs_perc_shift_lrv]

        # join the new data to the assemblage file
        assem_02 = assem.merge(ass2[cols], how='left', on=[self.accom_class_col, self.day_col, self.eval_time_col])

        # add the calibratedPotential.
        assem_02 = self.apply_percentile(assem=assem_02, ptile=current_calib_percentile, qfun=qfun)
        assem_02[self.calib_potential_col] = assem_02[self.calib_potential_col].fillna(CommonDOElements.__insufficientDataThresholdPotential__)
        assem[self.potential_col] = np.where(assem[self.pressure_col] > 0, np.log(assem[self.pressure_col]), np.nan)

        # label which ones trigger a run
        assem_02[self._has_run] = (assem_02[self.potential_col] >= assem_02[self.calib_potential_col]) * 1

        # calculate the max for each change
        assem_changes = assem_02.groupby([self.eval_time_col], as_index=False).agg(
            potential_exceeded=(self._has_run, 'max')
            , follows_reset_event=(self._follows_reset_event,'max')
            , max_shift_delta_lrv=(self._abs_perc_shift_delta_lrv, 'max')
            , max_shift_lrv=(self._abs_perc_shift_lrv, 'max')
            , max_shift_price=(self._abs_perc_shift_price, 'max')
        )

        # detect which evaluations have changes or triggered optimizations based on the current potential
        assem_changes[self._has_change] = ((assem_changes['max_shift_delta_lrv'] + assem_changes['max_shift_lrv'] + assem_changes['max_shift_price']) > 0) * 1
        assem_changes[self._has_action] = assem_changes[['potential_exceeded', self._has_change]].apply(lambda x: max(*x), axis=1)
        assem_changes[self._has_action] = assem_changes[self._has_action].fillna(0.)
        # determine which ones evaluation times have a change
        assem_changes[self._eval_day] = pd.to_datetime(assem_changes[self.eval_time_col]).dt.date

        # verify the sort order
        assem_changes = assem_changes.sort_values([self.eval_time_col], ascending=[True], ignore_index=True)

        assem_changes[self._action_order_count] = assem_changes.groupby(self._eval_day)[self._has_action].cumsum()

        # get the opts with change
        med_max_shift_lrv = assem_changes.query(f'{self._has_action}==1 and {self._action_order_count}<=@max_num_opts_per_day')['max_shift_lrv'].median()
        med_max_shift_delta_lrv = assem_changes.query(f'{self._has_action}==1 and {self._action_order_count}<=@max_num_opts_per_day')['max_shift_delta_lrv'].median()

        # now negotiate what changes are needed
        if prefer_observed:
            reference_target = current_nopts_per_day
        else:
            reference_target = current_target_avg_num_opts

        lrv_condition = ControlsConditions.determine_lrv_condition(med_max_shift_lrv=med_max_shift_lrv, min_shift_lrv_thresh=min_shift_lrv_thresh, max_shift_lrv_thresh=max_shift_lrv_thresh)
        delta_lrv_condition = ControlsConditions.determine_delta_lrv_condition(med_max_shift_delta_lrv=med_max_shift_delta_lrv
                                                                               , min_shift_delta_lrv_thresh=min_shift_delta_lrv_thresh, max_shift_delta_lrv_thresh=max_shift_delta_lrv_thresh)

        how_move = self.determine_how_move_from_conditions(lrv_condition=lrv_condition, delta_lrv_condtion=delta_lrv_condition)


        # Propose new target
        if how_move in [AdjustmentReason.potential_too_high_decrease_percentile,AdjustmentReason.special_decrease_percentile_condition]:
            target = reference_target + 1
        elif how_move in [AdjustmentReason.potential_too_low_increase_percentile, AdjustmentReason.special_increase_percentile_condition]:
            target = reference_target - 1
        else:
            target = reference_target

        # make adjustments to howMove based on waste and regret boundaries when evaluating the proposed Target
        if target > regret_threshold:
            if how_move == AdjustmentReason.special_decrease_percentile_condition:
                # if we have requested that we run more but can't run more then we have an unsatisfiable condition
                how_move = AdjustmentReason.unsatisfiable_conditions_when_decreasing_percentile
            else:
                how_move = AdjustmentReason.boundary_violation_recommends_too_high
            target = min((reference_target + regret_threshold) / 2, regret_threshold)
        elif target < waste_threshold:
            # if we have requested that we run more but can't run more then we have an unsatisfiable condition
            if how_move == AdjustmentReason.special_increase_percentile_condition:
                how_move = AdjustmentReason.unsatisfiable_conditions_when_increasing_percentile
            else:
                how_move = AdjustmentReason.boundary_violation_recommends_too_low
            target = max((reference_target + waste_threshold) / 2, waste_threshold)
        return target, how_move, med_max_shift_lrv, med_max_shift_delta_lrv

    def determine_how_move_from_conditions(self, lrv_condition:ControlsConditions, delta_lrv_condtion:ControlsConditions) -> AdjustmentReason:
        """
        There are 9 control regions here
        deltaLRV
        |   A   |   B   |   C
        |-------------------------------
        |   D   |   E   |   F
        |-------------------------------
        |   G   |   H   |   I
        |-------------------------------- LRV

        I am going to prioritize keeping changes in LRV in spec
        over delta LRV when there are conflicting conditons

        A&D: prioritize LRV here and run fewer if possible.
        F: LRV changes too high, DeltaLRV is in spec. We want to run more often
        I: Since we Prioritize keeping the LRV in spec we will increase the number of opts being run
        B: LRV is in-spec and DeltaLRV needs more frequent updating. Run more
        E: Every Thing is in-spec. No changes needed
        C: Both are too large, we need to check additional considerations
            - if C and we are running very many Opts, it may be an indication that we need to run
            more often.
            - if we are running too few opts then we definitely want to increase
        G: Both are too small, we need to check additioonal considerations
            - If G and we are running very few opts then this property is likely not good for dynamic optimization
            due to not having enough business. Though it could be seasonal.
        :param lrv_condition:
        :param delta_lrv_condtion:
        :param currentNoptsObserved:
        :return:
        """

        if lrv_condition == ControlsConditions.lrv_high and delta_lrv_condtion==ControlsConditions.delta_lrv_high:
            # region C. we need to increase the number of things being run
            return AdjustmentReason.special_decrease_percentile_condition
        elif lrv_condition==ControlsConditions.lrv_low and delta_lrv_condtion==ControlsConditions.delta_lrv_low:
            # Region G: we want to run fewer by increasing the percentile
            return AdjustmentReason.special_increase_percentile_condition
        elif lrv_condition==ControlsConditions.lrv_in_spec and delta_lrv_condtion == ControlsConditions.delta_lrv_in_spec:
            # Region E: we don't need to make any adjustments
            return AdjustmentReason.potential_in_spec
        elif lrv_condition==ControlsConditions.lrv_high:
            # Regions F&I: run more often
            return AdjustmentReason.potential_too_high_decrease_percentile
        elif lrv_condition==ControlsConditions.lrv_low:
            # regions A and D, we will attempt to run fewer
            return AdjustmentReason.potential_too_low_increase_percentile
        else:
            if delta_lrv_condtion.delta_lrv_low:
                # Region H: Run Less often because I want to increase deltaLRV
                return AdjustmentReason.potential_too_low_increase_percentile
            elif delta_lrv_condtion.delta_lrv_high:
                # region B: Run more often to decrease the deltaLRV
                return AdjustmentReason.potential_too_high_decrease_percentile




    def find_target_if_waste_or_regret(self, current_nopts_per_day: float, waste_threshold: float, regret_threshold: float):
        # regret threshold is the high value because we regret using credits to early
        # waste threshold is the low value because we wasted credits we could have used
        # calculate the number of days present
        # waste is days where there is not enough
        # regret is days there are too many
        if current_nopts_per_day >= regret_threshold or current_nopts_per_day < waste_threshold:
            # if we are running too few I want to bias this to run closer to the waste threshold. In my observations, this will be a much
            # safer choice resulting in smaller jumps.
            # if we are running too many I still want to make a big jump.
            if current_nopts_per_day<waste_threshold:
                test_target1 = (regret_threshold + waste_threshold) / 2
                test_target2 = waste_threshold
                test_target3 = waste_threshold + 1
                if current_nopts_per_day + 1 >= waste_threshold:
                    target = current_nopts_per_day + 1
                elif test_target1 == test_target3:
                    target=max(min(test_target2 + 0.5,(test_target2+test_target1) / 2), max(waste_threshold, current_nopts_per_day + 1))
                else:
                    target = test_target3
            else:
                target = (regret_threshold + waste_threshold) / 2

            if target > current_nopts_per_day:
                adjustment_type = AdjustmentReason.potential_too_high_oob_decrease_percentile
            else:
                adjustment_type = AdjustmentReason.potential_too_low_oob_increase_percentile
        else:
            target = current_nopts_per_day
            adjustment_type = AdjustmentReason.potential_in_spec
        return target, adjustment_type

    def generate_potentials_dict(self, ptile: float, qfun: dict) -> dict:
        return {
            last_modified: {
                accom_class: fun(ptile)
                for accom_class, fun in qfun_dict.items()
            }
            for last_modified, qfun_dict in qfun.items()
        }


    def apply_percentile(self, assem: pd.DataFrame, ptile: float, qfun: dict):
        # get the potentials for each accom class at the current percentile (ptile) and put into dataframe
        records = []
        for last_modified, qfun_dict in qfun.items():
            for accom_class, fun in qfun_dict.items():
                records.append([accom_class, fun(ptile), last_modified])

        test_calib = pd.DataFrame(records, columns=[
            self.accom_class_col,
            self.calib_potential_col,
            "last_modified"
        ])

        # column check to avoid column duplication
        if self.calib_potential_col in assem.columns:
            assem = assem.drop([self.calib_potential_col], axis=1)

        # copy the potential to the appropriate place
        return assem.merge(test_calib, how='inner', on=self.accom_class_col)

    def evaluate_percentile_complex(self, ECDO: EvalChangeDO, percentile: str, qfun: dict, n_days: float, eval_args: dict,
                                    generate_assemblage: bool = False, override_to_full_window_update:bool=False
                                    , include_scheduled_processings:bool=False, min_num_seconds_between_opts: int | float | None=None
                                    , max_num_dyn_opts_per_day: int | float | None=None, scheduled_process_times: list | None=None):

        percentile = min(100, percentile)
        potentials_dict = self.generate_potentials_dict(ptile=percentile, qfun=qfun)
        # print(potentialsDict)
        n_dyn_opts, opt_details_df = self.calculate_num_opts_with_simulation(ECDO=ECDO,
                                                                             potentials_dict=potentials_dict,
                                                                             eval_args=eval_args,
                                                                             generate_all_assemblage=generate_assemblage,
                                                                             override_to_full_window_update=override_to_full_window_update
                                                                             , include_scheduled_processings=include_scheduled_processings
                                                                             , min_num_seconds_between_opts=min_num_seconds_between_opts
                                                                             , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day
                                                                             , scheduled_process_times= scheduled_process_times)
        opt_details_df['percentile'] = percentile
        current_nopts_per_day = n_dyn_opts / n_days
        return current_nopts_per_day, opt_details_df


    def generate_simplified(self, assem: pd.DataFrame, qfun: dict):
        nDays = assem[self.caught_up_date_col].nunique()
        percentileList = []
        for ptile in range(1, 100):
            assem = self.apply_percentile(assem=assem, ptile=ptile, qfun=qfun)

            # calculate the number of optimizations that would be called at this point
            nDynOpts = self.calculate_num_opts_simple_version(assem=assem.copy())

            # get the average per day
            currentNoptsPerDay = nDynOpts / nDays
            percentileList.append([ptile, currentNoptsPerDay])
        return pd.DataFrame(percentileList, columns=['ptile', 'optCount'])


    def calculate_num_opts_with_simulation(self, ECDO: EvalChangeDO, potentials_dict: dict, eval_args: dict,
                                           generate_all_assemblage: bool = False, override_to_full_window_update:bool=False
                                           , include_scheduled_processings:bool=False, min_num_seconds_between_opts: int | float | None=None
                                           , max_num_dyn_opts_per_day: int | float | None=None, scheduled_process_times: list | None=None):

        ECDO2 = fast_deepcopy(ECDO)  # make a deep copy so that I don't modify the original state history

        initial_simulation_state = fast_deepcopy(obj=ECDO2.get_initial_simulation_state())
        # initialize
        rhs = RewriteHistorySimulation(current_state=initial_simulation_state, rc=self.rc, current_sim_time=ECDO2.get_sorted_evaluation_time()[0], override_to_full_window_update=override_to_full_window_update, min_num_seconds_between_opts=min_num_seconds_between_opts)
        n_opts, opt_details_df = rhs.simulate(ECDO=ECDO2, test_potentials=potentials_dict
                                              , include_scheduled_processings=include_scheduled_processings
                                              , max_num_dyn_opts_per_day=max_num_dyn_opts_per_day, **eval_args)
        return n_opts, opt_details_df

    def calculate_num_opts_simple_version(self, assem: pd.DataFrame, include_scheduled_processings:bool=False):
        # calculate the number of opts. Question: Do we want no days with waste/regret or average no waste/regret
        if include_scheduled_processings:
            assem[self._is_dyn_opt] = ((assem[self.potential_col] >= assem[self.calib_potential_col]) | (assem[self._prev_event].isin([ProcessDescription.CDP.value, ProcessDescription.BDE.value]))) * 1
        else:
            assem[self._is_dyn_opt] = (assem[self.potential_col] >= assem[self.calib_potential_col]) * 1
        dyn_opts = assem.groupby([self.caught_up_date_col, self.eval_time_col], as_index=False).agg(isOpt=(self._is_dyn_opt, 'max'))
        n_dyn_opts = dyn_opts[self._is_opt].sum()
        return n_dyn_opts

    def isAssemOpt(self, assemblage: pd.DataFrame):
        return bool(np.max(assemblage[self.potential_col] >= assemblage[self.calib_potential_col]))

    def generateCalibPotentialDataFrame(self, qfun: dict, currentCalibPercentile: float):
        invQfun = self.InverseQuantileToPotentialFunction(qfun)
        potDf = pd.DataFrame(
            [[accomClass, fun[accomClass](currentCalibPercentile)] for accomClass, fun in invQfun.items()],
            columns=[self.accom_class_col, self.calib_potential_col])
        return potDf

    def InverseQuantileToPotentialFunction(self, qfun: partial):
        return partial(np.interp, xp=qfun.keywords['fp'], fp=qfun.keywords['xp'])

    def find_target_percentile(self, calib_res: pd.DataFrame, target_avg_op: float, avg_calib_per_day_col: str, ptile_col: str):
        # get PL remembering that
        too_low = calib_res.query(f'{avg_calib_per_day_col}>=@target_avg_op').iloc[-1]
        too_high = calib_res.query(f'{avg_calib_per_day_col}<=@target_avg_op').iloc[0]
        if (too_high[ptile_col] == too_low[ptile_col]) or (too_high[avg_calib_per_day_col] == too_low[avg_calib_per_day_col]):
            return too_high[ptile_col]
        return self.calculate_target_percentile_by_linear_interp(target_nopts=target_avg_op,
                                                                 high_percentile=too_high[ptile_col],
                                                                 low_percentile=too_low[ptile_col],
                                                                 n_opts_high_perc=too_high[avg_calib_per_day_col],
                                                                 num_opts_low_perc=too_low[avg_calib_per_day_col])

    def calculate_target_percentile_by_linear_interp(self, target_nopts, high_percentile, low_percentile, num_opts_low_perc, n_opts_high_perc):
        w = (target_nopts - num_opts_low_perc) / (n_opts_high_perc - num_opts_low_perc)
        return w * high_percentile + (1 - w) * low_percentile
