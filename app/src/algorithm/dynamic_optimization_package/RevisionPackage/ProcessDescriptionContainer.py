from enum import Enum
import pandas as pd
import datetime

class ProcessDescription(Enum):
    BDE="BDE"
    CDP="CDP"
    CDP_ON_DEMAND="CDP_ON_DEMAND"
    UNMARKED="UNMARKED"
    NO_OPT="NO_OPT"

    @classmethod
    def _missing_(cls, value):
        return cls.UNMARKED


class ProcessInfoDO:
    # TODO: When we fix the reset_window in https://ideasinc.atlassian.net/browse/HEISEN-4618 this needs to be only BDE
    __ProcsForUsingFullWindow__={ProcessDescription.BDE,ProcessDescription.CDP,ProcessDescription.CDP_ON_DEMAND}
    __ScheduledProcessingTypes__ = {ProcessDescription.BDE,ProcessDescription.CDP}

    def __init__(self, process_type:ProcessDescription=ProcessDescription.NO_OPT, opt_window_end_date: pd.Timestamp | datetime.date | None=None, relevant_proc_time_for_scheduled: pd.Timestamp | None=None):
        self.process_type=process_type
        self._opt_window_end_date=opt_window_end_date
        self.relevant_proc_time_for_scheduled = relevant_proc_time_for_scheduled

    @property
    def opt_window_end_date(self)->pd.Timestamp:
        if isinstance(self._opt_window_end_date, datetime.date):
            return pd.to_datetime(self._opt_window_end_date)
        elif self.process_type in {ProcessDescription.NO_OPT, ProcessDescription.UNMARKED}:
            return pd.to_datetime(datetime.date.min)  # this prevents it from updating anything
        elif self._opt_window_end_date is None or self.process_type in ProcessInfoDO.__ProcsForUsingFullWindow__:
            return pd.to_datetime(datetime.date.max)  # this forces us to update everything
        else:
            return self._opt_window_end_date


    def set_end_window_with_current_date_and_window_size(self, currentDate: pd.Timestamp | datetime.date, windowSize:int):
        ...

    @property
    def is_scheduled(self)->bool:
        return self.process_type in ProcessInfoDO.__ScheduledProcessingTypes__

    @classmethod
    def generate_process_info(cls, procType:str, relevantProcTimeForScheduled:pd.Timestamp):
        return cls(process_type= ProcessDescription(procType), relevant_proc_time_for_scheduled=relevantProcTimeForScheduled)


