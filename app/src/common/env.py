import os
from typing import Callable

from src.common.enums.env_var import EnvironmentVariable


def get_value(var: EnvironmentVariable, default_value):
    return os.getenv(var.value, default_value)


def get_bool_value(var: EnvironmentVariable, default_value=False) -> bool:
    return str(get_value(var, default_value)).lower() in ['true', '1']


def get_value_or_default(var: EnvironmentVariable, default_value_supplier: Callable):
    env_value = os.getenv(var.value)
    return env_value if env_value is not None else default_value_supplier()


def get_int_value(var: EnvironmentVariable, default_value):
    value = get_value(var, default_value)
    return value if isinstance(value, int) else int(value)


IDP_COUNT_TABLE = get_value(EnvironmentVariable.IDP_COUNT_TABLE, '')
AVAILABLE_CAPACITY_TABLE = get_value(EnvironmentVariable.AVAILABLE_CAPACITY_TABLE, '')
IDP_WINDOW_TABLE = get_value(EnvironmentVariable.IDP_WINDOW_TABLE, '')
DECISION_CHANGE_TABLE = get_value(EnvironmentVariable.DECISION_CHANGE_TABLE, '')
S3_DATA_BUCKET_NAME = get_value(EnvironmentVariable.S3_DATA_BUCKET_NAME, '')
CALIBRATED_POTENTIAL_TABLE = get_value(EnvironmentVariable.CALIBRATED_POTENTIAL_TABLE, '')
ENV = get_value(EnvironmentVariable.ENV, 'prod')
