import logging
import threading
from time import sleep
from typing import override

from src.common.base_classes.base_handler import <PERSON>Handler
from src.common.enums.env_var import EnvironmentVariable
from src.common.env import get_value
from src.sqs.sqs_request_processor import SQSRequestProcessor


class SQSRequestListener(threading.Thread):

    log = logging.getLogger(__name__)

    @staticmethod
    def from_queue_url_and_handler(queue_url, request_handler: BaseHandler):
        return SQSRequestListener(SQSRequestProcessor(queue_url, request_handler))

    def __init__(self, sqs_request_processor: SQSRequestProcessor):
        self.sqs_request_processor = sqs_request_processor
        self.should_stop = False
        super().__init__()

    @override
    def run(self):
        self.log.info('Started poller.')
        while not self.should_stop:
            try:
                self.sqs_request_processor.process()
            except Exception as e:
                logging.exception(e)
            sleep(get_value(EnvironmentVariable.CALIB_REQ_POLL_INTERVAL, 2))
        self.log.info('Successfully stopped poller.')

    def stop(self):
        self.should_stop = True
