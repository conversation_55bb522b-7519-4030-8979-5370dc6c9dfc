import decimal
import logging
from datetime import date

import pandas as pd
from awswrangler import dynamodb
from boto3.dynamodb.conditions import Key, Attr

from src.common.dto.calibrated_potential import CalibratedPotential
from src.common.env import IDP_COUNT_TABLE, IDP_WINDOW_TABLE, DECISION_CHANGE_TABLE
from src.common.pydantic_util import from_dict_array


class DynamoDBService:

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def put_items(self, table_name, items):
        dynamodb.put_items(table_name=table_name, items=items)

    def update_potentials(self, table_name, potentials, client_code, property_code):
        items = [i.get_dynamodb_record(client_code, property_code) for i in potentials]
        self.put_items(table_name, items)

    def update_last_calib_date(self, client_code, property_code, caught_up_date=date.today().isoformat()):
        value = [{
            'clientCode_propertyCode': f'{client_code}_{property_code}',
            'calibrationItem': 'potentialCalibration',
            'calibDate': caught_up_date
        }]
        self.put_items('dyn-opt-calib-date', value)

    def update_last_threshold_revision_date(self, client_code, property_code, caught_up_date=date.today().isoformat()):
        value = [{
            'clientCode_propertyCode': f'{client_code}_{property_code}',
            'calibrationItem': 'potentialRevision',
            'calibDate': caught_up_date
        }]
        self.put_items('dyn-opt-calib-date', value)

    def fetch_items_for_evaluation(self, table_name, client_code, property_code) -> pd.DataFrame:
        df: pd.DataFrame = dynamodb.read_items(table_name=table_name,
                                               key_condition_expression=(Key('clientCode_propertyCode')
                                               .eq(
                                                   CalibratedPotential.get_partition_key(client_code, property_code))))

        if df.index.size == 0:
            return pd.DataFrame()
        decimal_columns = [k for k, v in df.iloc[0].items() if isinstance(v, decimal.Decimal)]
        for d in decimal_columns:
            df[d] = df[d].astype(float)
        return df

    def fetch_calibrated_potentials(self, table_name: str, client_code, property_code) -> list[CalibratedPotential]:
        items = dynamodb.read_items(table_name,
                                    key_condition_expression=(Key('clientCode_propertyCode').eq(
                                        CalibratedPotential.get_partition_key(client_code, property_code))),
                                    as_dataframe=False)
        return from_dict_array(list[CalibratedPotential], items)

    def fetch_calibrated_potentials_df(self, table_name: str, client_code, property_code) -> pd.DataFrame:
        df = dynamodb.read_items(table_name,
                                 key_condition_expression=(Key('clientCode_propertyCode').eq(
                                     CalibratedPotential.get_partition_key(client_code, property_code))))
        if df.empty:
            return pd.DataFrame()
        return df

    def fetch_idp_count(self, *, client_code, property_code, caught_up_dates):
        df = dynamodb.read_items(table_name=IDP_COUNT_TABLE,
                                 key_condition_expression=Key('clientCode_propertyCode').eq(
                                     fr'{client_code}_{property_code}'))
        if df.empty:
            return pd.DataFrame()
        if 'ttl' in df.columns:
            df.drop(columns=["ttl"])
        df['caughtUpDate'] = pd.to_datetime(df['caughtUpDate'])
        caught_up_dates: list[date] = [date.fromisoformat(str(c)) for c in caught_up_dates]
        return df.query('caughtUpDate in @caught_up_dates')

    def fetch_idp_window(self, *, client_code, property_code, caught_up_dates=None):
        filter_expression = None
        if caught_up_dates is not None and len(caught_up_dates) > 0:
            filter_expression = Attr('caughtUpDate').is_in(caught_up_dates)
        df = dynamodb.read_items(table_name=IDP_WINDOW_TABLE,
                                 key_condition_expression=Key('clientCode_propertyCode').eq(
                                     fr'{client_code}_{property_code}'),
                                 filter_expression=filter_expression)
        if df.empty:
            return pd.DataFrame()
        df['evaluationTime'] = pd.to_datetime(df['evaluationTime'])
        return df[['clientCode_propertyCode', 'evaluationTime', 'caughtUpDate', 'maxOccupancyDate']]

    def fetch_median_abs_lrv_change(self, *, client_code, property_code):
        df = dynamodb.read_items(table_name=DECISION_CHANGE_TABLE,
                                 key_condition_expression=Key('clientCode_propertyCode').eq(
                                     fr'{client_code}_{property_code}'))
        if df.empty:
            return pd.DataFrame()
        df['optimizationTime'] = pd.to_datetime(df['optimizationTime'])
        return df


dynamodb_service = DynamoDBService()
