import boto3
import csv

def upload_existing_csv_to_dynamodb(csv_file_path, table_name, region='us-east-2'):
    # Initialize DynamoDB resource
    dynamodb = boto3.resource('dynamodb', region_name=region)
    table = dynamodb.Table(table_name)

    # Open the CSV file and read contents
    with open(csv_file_path, mode='r', encoding='utf-8-sig') as file:
        reader = csv.DictReader(file)
        with table.batch_writer() as batch:
            for row in reader:
                # You can add custom type conversion if needed
                batch.put_item(Item=row)

    print(f"Data from '{csv_file_path}' uploaded successfully to DynamoDB table '{table_name}'.")

# Usage
upload_existing_csv_to_dynamodb('results.csv', 'dyn-opt-decision-change')
